00:13:32.915 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:13:32.923 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:13:38.921 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 360020 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:13:38.922 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:13:39.987 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:13:39.988 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:13:39.988 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:13:40.030 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:13:40.610 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:13:40.866 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:13:41.221 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:13:41.222 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:13:41.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:13:41.591 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:13:41.592 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:13:41.670 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:13:41.675 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:13:41.678 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:13:41.679 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:13:41.679 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:13:41.679 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:13:41.679 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:13:41.679 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:13:41.680 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:13:41.682 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:13:41.724 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:13:41.741 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.271 seconds (JVM running for 3.986)
00:13:49.417 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:13:49.467 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:13:49.468 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:13:49.468 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:13:49.468 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:13:49.646 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:13:49.646 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:13:49.698 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:13:49.699 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:13:49.757 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:13:49.758 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:13:49.766 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:13:49.766 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:13:49.768 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:13:49.768 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:13:49.832 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:13:49.841 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:13:49.841 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:13:49.843 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:13:49.844 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:13:49.844 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:13:49.845 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:13:49.845 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:13:49.847 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:13:49.850 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:13:49.850 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:13:49.851 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2083] - 应用JSON格式表头合并单元格，数量: 7
00:13:49.852 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:13:49.857 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:13:49.858 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:13:49.859 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:13:49.859 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:13:49.861 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:13:49.861 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:13:49.862 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:13:49.863 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:13:49.985 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3225 bytes
00:13:49.991 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_001349.docx, 大小: 3225 bytes
00:14:39.241 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:14:39.241 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:14:39.241 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:14:39.242 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:14:39.243 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:14:39.244 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:14:39.246 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:14:39.246 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:14:39.254 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:14:39.255 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:14:39.255 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:14:39.255 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:14:39.255 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:14:39.255 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:14:39.256 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:14:39.257 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:14:39.257 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:14:39.258 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:14:39.258 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:14:39.258 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:14:39.259 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:14:39.259 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:14:39.260 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:14:39.261 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:14:39.261 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:14:39.262 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2083] - 应用JSON格式表头合并单元格，数量: 7
00:14:39.263 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:14:39.264 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:14:39.265 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:14:39.266 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:14:39.267 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:14:39.267 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:14:39.268 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:14:39.269 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:14:39.270 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:14:39.277 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3225 bytes
00:14:39.280 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_001439.docx, 大小: 3225 bytes
00:16:46.249 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:16:46.251 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:16:50.861 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 364092 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:16:50.863 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:16:51.798 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:16:51.799 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:16:51.799 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:16:51.837 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:16:52.448 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:16:52.677 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:16:53.024 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:16:53.026 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:16:53.355 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:16:53.385 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:16:53.386 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:16:53.462 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:16:53.467 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:16:53.469 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:16:53.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:16:53.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:16:53.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:16:53.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:16:53.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:16:53.471 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:16:53.472 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:16:53.514 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:16:53.530 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.026 seconds (JVM running for 3.606)
00:16:57.646 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:16:57.703 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:16:57.704 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:16:57.704 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:16:57.704 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:16:57.897 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:16:57.897 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:16:57.945 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:16:57.946 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:16:57.998 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:16:57.998 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:16:58.000 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:16:58.001 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:16:58.002 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:16:58.002 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:16:58.040 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:17:12.113 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:17:12.114 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:17:12.115 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:17:12.116 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:17:12.116 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:17:12.116 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:17:12.116 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:17:12.119 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:17:16.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:17:16.251 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:17:16.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2082] - 应用JSON格式表头合并单元格，数量: 7
00:17:16.253 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:17:16.257 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:17:16.257 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:17:16.258 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:17:16.259 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:17:16.260 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:17:16.261 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:17:16.262 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:17:16.263 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:17:16.331 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3225 bytes
00:17:16.337 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_001716.docx, 大小: 3225 bytes
00:18:25.233 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:18:25.234 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:18:25.234 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:18:25.234 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:18:25.236 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:18:25.236 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:18:25.239 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:18:25.239 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:18:25.246 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:18:25.247 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:18:25.247 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:18:25.248 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:18:25.248 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:18:25.248 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:18:25.249 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:19:28.558 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:19:28.558 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:19:28.559 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:19:28.560 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:19:28.561 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:19:28.561 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:19:28.561 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:19:28.563 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:23:14.362 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1935] - 增强版嵌套表格创建成功
00:23:14.363 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:23:14.364 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2082] - 应用JSON格式表头合并单元格，数量: 7
00:23:14.364 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:23:14.365 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:23:14.366 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:23:14.367 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:23:14.367 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:23:14.368 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:23:14.368 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:23:14.369 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:23:14.369 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2125] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:23:14.376 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3224 bytes
00:23:14.378 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_002314.docx, 大小: 3224 bytes
00:26:12.810 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:26:12.813 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:26:17.419 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 375629 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:26:17.420 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:26:18.375 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:26:18.376 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:26:18.376 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:26:18.416 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:26:19.008 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:26:19.246 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:26:19.607 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:26:19.608 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:26:19.989 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:26:20.022 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:26:20.023 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:26:20.110 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:26:20.115 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:26:20.118 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:26:20.119 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:26:20.119 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:26:20.119 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:26:20.119 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:26:20.120 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:26:20.120 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:26:20.122 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:26:20.163 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:26:20.181 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.142 seconds (JVM running for 3.725)
00:26:26.001 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:26:26.062 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:26:26.062 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:26:26.062 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:26:26.063 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:26:26.252 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:26:26.252 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:26:26.302 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:26:26.302 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:26:26.352 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:26:26.352 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:26:26.354 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:26:26.354 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:26:26.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:26:26.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:26:26.394 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:28:48.230 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1936] - 增强版嵌套表格创建成功
00:28:48.230 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:28:48.232 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:28:48.232 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:28:48.233 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:28:48.233 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:28:48.233 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:28:48.235 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:28:48.236 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1936] - 增强版嵌套表格创建成功
00:28:48.236 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:28:48.237 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2083] - 应用JSON格式表头合并单元格，数量: 7
00:28:48.237 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:28:48.240 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:28:48.241 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:28:48.242 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:28:48.243 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:28:48.244 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:28:48.244 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:28:48.245 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:28:48.246 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2126] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:28:48.306 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3224 bytes
00:28:48.311 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_002848.docx, 大小: 3224 bytes
00:29:13.313 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:29:13.315 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:29:17.593 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 379414 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:29:17.594 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:29:18.524 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:29:18.525 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:29:18.525 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:29:18.565 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:29:19.269 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:29:19.513 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:29:19.877 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:29:19.878 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:29:20.202 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:29:20.232 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:29:20.233 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:29:20.312 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:29:20.317 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:29:20.319 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:29:20.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:29:20.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:29:20.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:29:20.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:29:20.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:29:20.321 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:29:20.323 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:29:20.363 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:29:20.378 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.142 seconds (JVM running for 3.711)
00:29:22.914 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:29:22.965 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:29:22.965 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:29:22.966 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:29:22.966 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:29:23.139 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:29:23.140 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:29:23.188 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:29:23.188 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:29:23.235 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:29:23.236 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:29:23.237 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:29:23.238 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:29:23.239 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:29:23.239 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:29:23.275 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:31:56.183 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1938] - 增强版嵌套表格创建成功
00:31:56.183 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:31:56.185 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:31:56.186 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:31:56.186 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:31:56.186 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:31:56.186 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:31:56.188 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1712] - 使用WordTableExample方式创建嵌套表格成功
00:31:57.360 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableEnhanced,1938] - 增强版嵌套表格创建成功
00:31:57.361 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:31:57.362 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2085] - 应用JSON格式表头合并单元格，数量: 7
00:31:57.363 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2128] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:31:57.366 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2128] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:31:57.367 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2128] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:31:57.368 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2128] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:31:57.368 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:31:57.370 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:31:57.371 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2128] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:31:57.372 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2128] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:31:57.373 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2128] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:31:57.460 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3224 bytes
00:31:57.469 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_003157.docx, 大小: 3224 bytes
00:32:59.502 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:32:59.504 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:33:04.166 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 384249 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:33:04.167 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:33:05.113 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:33:05.114 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:33:05.114 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:33:05.154 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:33:05.748 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:33:06.002 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:33:06.359 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:33:06.360 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:33:07.802 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:33:07.831 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:33:07.832 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:33:07.914 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:33:07.919 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:33:07.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:33:07.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:33:07.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:33:07.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:33:07.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:33:07.923 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:33:07.924 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:33:07.925 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:33:07.965 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:33:07.981 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.076 seconds (JVM running for 3.634)
00:33:18.708 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:33:18.751 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:33:18.752 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:33:18.752 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:33:18.752 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:33:18.927 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:33:18.927 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:33:18.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:33:18.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:33:19.033 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:33:19.033 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:33:19.035 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:33:19.035 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:33:19.036 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:33:19.036 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:33:19.078 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1715] - 使用WordTableExample方式创建嵌套表格成功
00:33:19.079 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1766] - 使用文本方式表示嵌套表格: 3行 x 3列
00:33:19.081 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1803] - 嵌套表格文本内容添加完成
00:33:19.081 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:33:19.082 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:33:19.083 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:33:19.083 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:33:19.084 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:33:19.084 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:33:19.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1715] - 使用WordTableExample方式创建嵌套表格成功
00:33:19.087 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1766] - 使用文本方式表示嵌套表格: 3行 x 3列
00:33:19.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1803] - 嵌套表格文本内容添加完成
00:33:19.088 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:33:19.089 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2071] - 应用JSON格式表头合并单元格，数量: 7
00:33:19.090 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2114] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:33:19.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2114] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:33:19.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2114] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:33:19.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2114] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:33:19.094 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:33:19.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:33:19.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2114] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:33:19.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2114] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:33:19.096 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2114] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:33:19.174 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3363 bytes
00:33:19.183 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_003319.docx, 大小: 3363 bytes
00:43:18.038 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:43:18.040 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:43:23.022 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 396649 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:43:23.023 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:43:23.962 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:43:23.962 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:43:23.963 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:43:24.002 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:43:24.567 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:43:24.808 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:43:25.156 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:43:25.157 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:43:25.486 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:43:25.516 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:43:25.518 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:43:25.604 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:43:25.609 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:43:25.612 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:43:25.612 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:43:25.612 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:43:25.612 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:43:25.612 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:43:25.613 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:43:25.613 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:43:25.615 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:43:25.657 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:43:25.674 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.027 seconds (JVM running for 3.577)
00:43:35.251 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:43:35.307 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:43:35.308 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:43:35.308 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:43:35.308 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:43:35.517 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:43:35.518 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:43:35.563 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:43:35.563 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:43:35.617 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:43:35.617 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:43:35.619 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:43:35.619 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:43:35.620 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:43:35.620 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:43:35.656 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1778] - 使用文本方式表示嵌套表格: 3行 x 3列
00:43:35.660 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1815] - 嵌套表格文本内容添加完成
00:43:35.660 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:43:35.662 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:43:35.663 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:43:35.663 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:43:35.663 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:43:35.664 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建真正的嵌套表格
00:43:35.667 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1778] - 使用文本方式表示嵌套表格: 3行 x 3列
00:43:35.669 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTableAsText,1815] - 嵌套表格文本内容添加完成
00:43:35.669 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:43:35.670 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2037] - 应用JSON格式表头合并单元格，数量: 7
00:43:35.671 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2080] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:43:35.675 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2080] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:43:35.676 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2080] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:43:35.676 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2080] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:43:35.677 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:43:35.678 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:43:35.678 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2080] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:43:35.679 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2080] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:43:35.680 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2080] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:43:35.753 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3383 bytes
00:43:35.764 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_004335.docx, 大小: 3383 bytes
00:45:33.001 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:45:33.004 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:45:37.246 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 399604 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:45:37.247 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:45:38.178 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:45:38.179 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:45:38.179 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:45:38.216 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:45:38.796 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:45:39.026 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:45:39.368 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:45:39.369 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:45:39.694 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:45:39.722 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:45:39.723 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:45:39.800 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:45:39.806 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:45:39.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:45:39.809 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:45:39.810 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:45:39.810 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:45:39.810 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:45:39.810 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:45:39.811 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:45:39.812 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:45:39.850 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:45:39.867 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.987 seconds (JVM running for 3.52)
00:46:34.593 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:46:34.643 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
00:46:34.643 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
00:46:34.643 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,83] - 检测到嵌套表格，使用增强导出模式
00:46:34.643 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1263] - 开始导出新JSON格式Word文档，表格标题: null
00:46:34.822 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
00:46:34.823 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1353] - 创建新JSON格式表格，总行数: 4, 总列数: 8
00:46:34.876 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1445] - 设置表格总宽度: 960px (14400twips)
00:46:34.876 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1377] - 开始处理表头，表头行数: 2
00:46:34.925 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1389] - 表头处理完成，当前行索引: 2
00:46:34.925 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:46:34.927 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 详细检查结果
00:46:34.927 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 详细检查结果
00:46:34.928 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:46:34.929 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建嵌套表格
00:46:34.966 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:46:34.968 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1525] - 设置数据行高度: 208px (3120twips)
00:46:34.968 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1540] - 检测到嵌套表格，行: 2, 列: 2, 主内容: 测试数据
00:46:34.969 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1574] - 开始处理嵌套表格，父单元格内容: 测试数据
00:46:34.969 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1654] - 创建嵌套表格: 3行 x 3列
00:46:34.969 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createNestedTable,1660] - 开始创建嵌套表格
00:46:34.971 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1632] - 嵌套表格处理完成
00:46:34.972 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,2032] - 应用JSON格式表头合并单元格，数量: 7
00:46:34.973 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:46:34.975 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:46:34.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:46:34.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:46:34.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,953] - 应用水平合并: 行0, 列3-4, 跨度2列
00:46:34.978 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,994] - 水平合并完成: 行0, 列3-4
00:46:34.979 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:46:34.980 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:46:34.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,2075] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:46:35.045 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1284] - 新JSON格式Word文档导出完成，文件大小: 3241 bytes
00:46:35.053 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250827_004635.docx, 大小: 3241 bytes
08:32:41.472 [http-nio-9550-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
08:32:41.476 [http-nio-9550-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
08:32:41.477 [http-nio-9550-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
08:32:46.315 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
08:32:46.315 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
08:32:46.317 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
08:32:46.317 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
08:33:55.186 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
08:33:55.187 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
08:33:55.189 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
08:33:55.189 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
08:59:11.999 [http-nio-9550-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
08:59:12.001 [http-nio-9550-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
08:59:12.001 [http-nio-9550-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
08:59:16.269 [http-nio-9550-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
08:59:16.270 [http-nio-9550-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
08:59:16.272 [http-nio-9550-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
08:59:16.273 [http-nio-9550-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
