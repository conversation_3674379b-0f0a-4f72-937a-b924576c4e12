16:34:34.436 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 20189 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
16:34:34.440 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:34:35.403 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
16:34:35.404 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:34:35.404 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:34:35.445 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:34:36.027 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:34:36.278 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:34:36.667 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
16:34:36.667 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
16:34:36.921 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
16:34:36.946 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
16:34:36.947 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
16:34:37.014 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
16:34:37.019 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
16:34:37.021 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
16:34:37.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
16:34:37.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
16:34:37.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
16:34:37.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
16:34:37.022 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
16:34:37.023 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
16:34:37.024 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
16:34:37.059 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
16:34:37.073 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.051 seconds (JVM running for 3.425)
16:45:11.405 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:45:17.434 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
16:45:17.434 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
16:45:17.443 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
16:45:17.444 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
16:54:08.087 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:54:08.090 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
