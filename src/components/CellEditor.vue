<template>
  <div class="cell-editor-wrapper" :style="wrapperStyle">
    <!-- 非编辑状态：显示格式化内容 -->
    <div
      v-if="!isEditing"
      class="cell-display"
      :class="{ 'has-math': hasMath }"
      :title="content"
      @click="startEdit"
      @dblclick="startEditWithSelection"
    >
      <span v-html="formattedContent"></span>
    </div>

    <!-- 编辑状态：显示输入框 -->
    <textarea
      v-if="isEditing"
      ref="editor"
      v-model="editingContent"
      class="cell-editor"
      :style="editorStyle"
      @blur="finishEdit"
      @keydown="handleKeyDown"
      @input="handleInput"
      @compositionstart="handleCompositionStart"
      @compositionend="handleCompositionEnd"
      @paste="handlePaste"
      @focus="handleFocus"
    ></textarea>
  </div>
</template>

<script>
export default {
  name: 'CellEditor',
  props: {
    // 单元格内容
    content: {
      type: String,
      default: ''
    },
    // 是否包含数学公式
    hasMath: {
      type: Boolean,
      default: false
    },
    // 单元格宽度
    width: {
      type: [String, Number],
      default: 'auto'
    },
    // 单元格高度
    height: {
      type: [String, Number],
      default: 'auto'
    },
    // 最小高度
    minHeight: {
      type: [String, Number],
      default: 50
    },
    // 是否自动聚焦
    autoFocus: {
      type: Boolean,
      default: false
    },
    // 是否全选内容
    selectAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false,
      editingContent: '',
      originalContent: '',
      isComposing: false, // 中文输入法状态
      mathJaxReady: false
    }
  },
  computed: {
    // 格式化显示内容
    formattedContent() {
      if (!this.content) return ''

      // HTML 转义
      const escaped = this.content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')

      // 换行符转换为 <br>
      return escaped.replace(/\n/g, '<br>')
    },

    // 编辑器样式（基础样式，主要样式在 CSS 中定义）
    editorStyle() {
      const style = {}

      // 设置最小高度 - 总是应用传入的最小高度
      const minHeightValue = typeof this.minHeight === 'number' ? this.minHeight : parseInt(this.minHeight) || 50
      style.minHeight = `${minHeightValue}px`

      // 设置宽度
      if (this.width !== 'auto') {
        style.width = typeof this.width === 'number' ? `${this.width}px` : this.width
      }

      // 设置高度 - 对于指定高度的单元格，使用固定高度
      if (this.height !== 'auto') {
        const heightValue = typeof this.height === 'number' ? `${this.height}px` : this.height
        style.height = heightValue
        style.minHeight = heightValue
        // 对于固定高度单元格，禁用自动调整高度
        style.resize = 'none'
      }

      return style
    },

    // 包装器样式
    wrapperStyle() {
      const style = {}

      // 设置包装器的最小高度
      const minHeightValue = typeof this.minHeight === 'number' ? this.minHeight : parseInt(this.minHeight) || 50
      style.minHeight = `${minHeightValue}px`

      // 如果指定了高度，也应用到包装器
      if (this.height !== 'auto') {
        const heightValue = typeof this.height === 'number' ? `${this.height}px` : this.height
        style.height = heightValue
      }

      return style
    },

    // 判断是否为合并单元格（高度不为auto时认为是合并单元格）
    isMergedCell() {
      return this.height !== 'auto' && this.height > 50
    }
  },
  watch: {
    content: {
      handler(newContent) {
        if (!this.isEditing) {
          this.editingContent = newContent || ''
        }
      },
      immediate: true
    },
    autoFocus: {
      handler(shouldFocus) {
        if (shouldFocus && !this.isEditing) {
          this.startEdit()
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initializeMathJax()
  },
  methods: {
    // 开始编辑
    startEdit() {
      console.log('开始编辑单元格')
      this.isEditing = true
      this.originalContent = this.content || ''
      this.editingContent = this.content || ''

      this.$emit('start-edit')

      this.$nextTick(() => {
        this.focusEditor()
      })
    },

    // 开始编辑并全选内容
    startEditWithSelection() {
      console.log('开始编辑单元格（全选模式）')
      this.startEdit()

      this.$nextTick(() => {
        this.selectAllContent()
      })
    },

    // 聚焦编辑器
    focusEditor() {
      console.log('尝试聚焦编辑器...')
      if (this.$refs.editor) {
        console.log('找到编辑器元素，开始聚焦')
        this.$refs.editor.focus()

        // 自动调整高度
        this.autoResizeTextarea()

        if (this.selectAll) {
          this.selectAllContent()
        } else {
          // 将光标移到末尾
          const editor = this.$refs.editor
          editor.setSelectionRange(editor.value.length, editor.value.length)
        }
        console.log('编辑器聚焦完成')
      } else {
        console.error('未找到编辑器元素 $refs.editor')
      }
    },

    // 全选内容
    selectAllContent() {
      if (this.$refs.editor) {
        this.$refs.editor.select()
      }
    },

    // 完成编辑
    finishEdit() {
      if (this.isComposing) {
        console.log('中文输入中，延迟完成编辑')
        return
      }

      console.log('完成编辑，内容:', this.editingContent)
      this.isEditing = false

      // 检查内容是否有变化
      if (this.editingContent !== this.originalContent) {
        this.$emit('content-change', this.editingContent)
      }

      this.$emit('finish-edit', this.editingContent)

      // 如果包含数学公式，触发渲染
      if (this.containsMath(this.editingContent)) {
        this.$nextTick(() => {
          this.renderMathJax()
        })
      }
    },

    // 取消编辑
    cancelEdit() {
      console.log('取消编辑')
      this.isEditing = false
      this.editingContent = this.originalContent

      this.$emit('cancel-edit')
    },

    // 处理键盘事件
    handleKeyDown(event) {
      console.log('键盘事件:', event.key, 'Shift:', event.shiftKey, 'Ctrl:', event.ctrlKey, '中文输入:', this.isComposing)

      // 如果正在中文输入，不处理特殊键
      if (this.isComposing) {
        return
      }

      switch (event.key) {
        case 'Enter':
          if (event.shiftKey || event.ctrlKey) {
            // Shift+Enter 或 Ctrl+Enter: 插入换行符
            console.log('插入换行符')
            // textarea 默认支持换行，不需要特殊处理
          } else {
            // 普通 Enter: 完成编辑
            event.preventDefault()
            this.finishEdit()
          }
          break

        case 'Escape':
          // Esc: 取消编辑
          event.preventDefault()
          this.cancelEdit()
          break

        case 'Tab':
          // Tab: 完成编辑并移动到下一个单元格
          event.preventDefault()
          this.finishEdit()
          this.$emit('move-next', event.shiftKey ? 'prev' : 'next')
          break
      }
    },

    // 处理输入事件
    handleInput(event) {
      console.log('输入事件触发:', this.editingContent)
      if (this.isComposing) {
        console.log('中文输入中，跳过处理')
        return
      }

      // 自动调整高度
      this.autoResizeTextarea()

      // 实时更新内容
      this.$emit('input', this.editingContent)
      console.log('发送输入事件:', this.editingContent)
    },

    // 处理中文输入开始
    handleCompositionStart() {
      console.log('中文输入开始')
      this.isComposing = true
    },

    // 处理中文输入结束
    handleCompositionEnd() {
      console.log('中文输入结束')
      this.isComposing = false

      // 自动调整高度
      this.autoResizeTextarea()

      // 输入法结束后触发输入事件
      this.$emit('input', this.editingContent)
    },

    // 处理粘贴事件
    handlePaste(event) {
      console.log('粘贴事件')
      // 允许默认粘贴行为
      this.$nextTick(() => {
        this.autoResizeTextarea()
        this.$emit('input', this.editingContent)
      })
    },

    // 处理聚焦事件
    handleFocus(event) {
      console.log('编辑器获得焦点')
      this.autoResizeTextarea()
    },

    // 自动调整 textarea 高度
    autoResizeTextarea() {
      // 对于合并单元格，不进行自动调整高度
      if (this.isMergedCell) {
        return
      }

      this.$nextTick(() => {
        const textarea = this.$refs.editor
        if (!textarea) return

        // 重置高度以获取正确的 scrollHeight
        textarea.style.height = 'auto'

        // 计算所需高度
        const minHeight = parseInt(this.minHeight) || 50
        const scrollHeight = textarea.scrollHeight
        const newHeight = Math.max(minHeight, scrollHeight)

        // 设置新高度
        textarea.style.height = `${newHeight}px`

        console.log('自动调整高度:', {
          minHeight,
          scrollHeight,
          newHeight,
          content: this.editingContent,
          isMerged: this.isMergedCell
        })
      })
    },

    // 检测是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 初始化 MathJax
    async initializeMathJax() {
      try {
        // 检查 MathJax 是否已加载
        if (window.MathJax) {
          this.mathJaxReady = true
          return
        }

        // 等待 MathJax 加载
        let attempts = 0
        while (!window.MathJax && attempts < 50) {
          await new Promise(resolve => setTimeout(resolve, 100))
          attempts++
        }

        if (window.MathJax) {
          this.mathJaxReady = true
          console.log('MathJax 已准备就绪')
        }
      } catch (error) {
        console.error('MathJax 初始化失败:', error)
      }
    },

    // 渲染 MathJax
    async renderMathJax() {
      try {
        if (!this.mathJaxReady || !window.MathJax) {
          console.warn('MathJax 未准备就绪')
          return
        }

        const displayElement = this.$el.querySelector('.cell-display')
        if (displayElement && window.MathJax.typesetPromise) {
          await window.MathJax.typesetPromise([displayElement])
          console.log('MathJax 渲染完成')
        }
      } catch (error) {
        console.error('MathJax 渲染失败:', error)
      }
    },

    // 外部调用：进入编辑模式
    edit(selectAll = false) {
      if (selectAll) {
        this.startEditWithSelection()
      } else {
        this.startEdit()
      }
    },

    // 外部调用：获取当前内容
    getContent() {
      return this.isEditing ? this.editingContent : this.content
    },

    // 外部调用：设置内容
    setContent(content) {
      if (this.isEditing) {
        this.editingContent = content
      } else {
        this.$emit('content-change', content)
      }
    }
  }
}
</script>

<style scoped>
.cell-editor-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  max-width: 100%;
  overflow: hidden;
}

.cell-display {
  width: 100%;
  height: 100%;
  padding: 0;
  cursor: text;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.4;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.cell-display:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.cell-display.has-math {
  background-color: rgba(40, 167, 69, 0.05);
}

.cell-editor {
  width: 100%;
  max-width: 100%;
  padding: 0;
  border: none;
  border-radius: 0;
  background-color: rgba(255, 255, 255, 0.98);
  font-family: inherit;
  font-size: inherit;
  line-height: 1.4;
  resize: none;
  box-sizing: border-box;
  display: block;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-word;
  white-space: pre-wrap;
  box-shadow: inset 0 0 0 1px rgba(0, 123, 255, 0.4);
  transition: box-shadow 0.15s ease, background-color 0.15s ease;
}

.cell-editor:focus {
  outline: none;
  background-color: #ffffff;
  box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.7);
}

/* 自定义滚动条样式 - 更细更美观 */
.cell-editor::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.cell-editor::-webkit-scrollbar-track {
  background: transparent;
}

.cell-editor::-webkit-scrollbar-thumb {
  background: rgba(0, 123, 255, 0.2);
  border-radius: 2px;
  transition: all 0.2s ease;
}

.cell-editor::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 123, 255, 0.4);
}

.cell-editor::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.cell-editor {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 123, 255, 0.2) transparent;
}

/* 编辑器聚焦时的滚动条样式 */
.cell-editor:focus::-webkit-scrollbar-thumb {
  background: rgba(0, 123, 255, 0.3);
}

.cell-editor:focus::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 123, 255, 0.5);
}

/* 数学公式样式 */
.cell-display.has-math .MathJax {
  font-size: inherit !important;
}
</style>
